# Set the host name for URL creation
require 'rubygems'
require 'sitemap_generator'
require 'carrierwave'
# Set the host name for URL creation
SitemapGenerator::Sitemap.default_host = 'https://www.shopmuzai.com'
SitemapGenerator::Sitemap.sitemaps_host = "https://#{ENV['S3_BUCKET']}.s3.amazonaws.com"
SitemapGenerator::Sitemap.public_path = 'tmp/'
SitemapGenerator::Sitemap.sitemaps_path = 'sitemaps/luxe'
SitemapGenerator::Sitemap.adapter = SitemapGenerator::WaveAdapter.new
SitemapGenerator::Sitemap.create do
  # Put links creation logic here.
  #
  # The root path '/' and sitemap index file are added automatically for you.
  # Links are added to the Sitemap in the order they are specified.
  #
  # Usage: add(path, options={})
  #        (default options are used if you don't specify)
  #
  # Defaults: :priority => 0.5, :changefreq => 'weekly',
  #           :lastmod => Time.now, :host => default_host
  #
  # Examples:
  #
  # Add '/articles'
  #
  #   add articles_path, :priority => 0.7, :changefreq => 'daily'
  #
  # Add all articles:
  #
  #   Article.find_each do |article|
  #     add article_path(article), :lastmod => article.updated_at
  #   end

  add '/', :changefreq => 'daily', :priority => 0.9
  Designer.where(designer_type: "Tier 1 Designer", state_machine: ['approved', 'review', 'vacation']).each do |designer|
    add "/#{designer.cached_slug}", :priority => 0.6, :changefreq => 'weekly'
  end

  Category.where(app_name: "luxe").each do |category|
    add "/#{category.name.downcase}", :priority => 0.6, :changefreq => 'weekly'
  end

  Designer.where(designer_type: "Tier 1 Designer", state_machine: ['approved', 'review', 'vacation']).each do |designer|
    designer.designs.each do |design|
      add "/#{designer.cached_slug}/buy/#{design.title.parameterize}/#{design.id}", :priority => 0.6, :changefreq => 'weekly'
    end
  end

  #SitemapGenerator::Sitemap.ping_search_engines
end
