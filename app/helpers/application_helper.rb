module ApplicationHelper
  DEFAULT_KEYWORDS = SystemConstant.get('DEFAULT_KEYWORDS')
  DEFAULT_DESCRIPTION = SystemConstant.get('DEFAULT_DESCRIPTION')
  DEFAULT_OG_TITLE = SystemConstant.get('DEFAULT_OG_TITLE')
  DEFAULT_TITLE = SystemConstant.get('DEFAULT_TITLE')
  ADDRESS = SystemConstant.get('ADDRESS')
  PINCODE = SystemConstant.get('PINCODE')
  TELEPHONE = SystemConstant.get('TELEPHONE')
  TEAM_MAIL_ID = SystemConstant.get('TEAM_MAIL_ID')
  DESIGN_DESCRIPTION = SystemConstant.get('DESIGN_DESCRIPTION')
  SEO_CONTENT = [:title, :description, :keywords, :og_title, :og_image, :og_url, :og_type, :canonical_path]

  # Read Application Specific Constants
  #
  # == Returns:
  # STRING
  #
  def get_constant(constant_name)
    ApplicationHelper.const_get(constant_name.to_sym)
  end

  def nav_link(link_text, link_path, difference)
    class_name = current_page?(link_path) ? 'active' : ''

    content_tag(:a, :href => link_path) do
      content_tag(:div, link_text, class: "small-6 columns order_return_links text-center #{class_name} #{difference}")
    end
  end

  def check_for_collection_noindex
    (params[:controller] == "store" && params[:action] == "catalog_page" && ( %w(wedding-sarees wedding-salwars wedding-lehengas wedding-jewellery).include?(params[:collection])))
  end
  
  def check_for_catalogue_noindex
    (params[:controller] == "store" && params[:action] == "catalog_page" && params[:catalogue].present?)
  end

  # Create SEO related helper methods
  #
  # == Returns:
  # content_for details
  #
  SEO_CONTENT.each do |method_name|
    self.send :define_method, method_name do |page_content, options={}|
      content_for(method_name, page_content.to_s)
    end
  end

  SORT_TYPE = {
    # 'Top Rated' => 'top_rated',
    'Popularity' => 'bstslr',
    'Price - High to Low' => 'h2l',
    'Price - Low to High' => 'l2h',
    'Discounts' => 'discount',
    'Newest First' => 'new',
    'HandPicked' => 'default',
    'Trending' => 'trending',
    'Most Ordered' => 'trending-designs',
    'Recently Popular' => 'recent-30'
  }

  UNBXD_SORT = ['Popularity', 'Price - High to Low', 'Price - Low to High', 'Discounts','Newest First','HandPicked','Trending','Most Ordered','Recently Popular']

  UNBXD_SORT_TYPE = {
    'Popularity' => 'sell_count desc',
    'Price - High to Low' => 'discount_price desc',
    'Price - Low to High' => 'discount_price asc',
    'Discounts' => 'discount_percent desc',
    'Newest First' => 'created_at desc',
    'HandPicked' => 'grade desc',
    'Trending' => 'sell_count desc',
    'Most Ordered' => 'sell_count desc',
    'Recently Popular' => 'sell_count desc'
  }

  # Get Price with currency
  #
  # == Returns:
  # CURRENCY PRICE
  #
  def get_price_with_currency(price, currency)
    "#{currency} #{price.round(CurrencyConvert.round_to)}"
  end

  def rts_available_country?(country)
    RTS_ALLOWED_COUNTRIES.include?(country.try(:downcase))
  end

  # Get Price with symbol
  #
  # == Returns:
  # SYMBOL PRICE
  #

  # Get symbol from gex_code
  #
  # == Returns:
  # CURRENCY SYMBOL
  #
  def get_symbol_from(hex_code)
    codes = hex_code.split(', ')
    symbol = ''
    codes.each do |code|
      symbol += [code.hex].pack('U')
    end
    symbol
  end

  def get_price_with_symbol_currency(price, hex_code, floor_val=false, rate = nil)
    total_price = get_price_in_currency(price,rate)
    if floor_val == true
      total_price = total_price.floor
    end
    "#{get_symbol_from(hex_code)}#{total_price}"
  end


  def get_campaign_data
    session.to_hash.symbolize_keys!.slice(:utm_source,:utm_medium,:utm_campaign,:utm_term,:utm_content,:icn_term).merge({ip_address: request.remote_ip,app_source: 'Mobile'})
  end

  def is_domestic?
    @country_code == 'IN'
  end

  def is_saudi_arabia?
    @country_code == 'SA'
  end

  def is_uae?
    @country_code == 'AE'
  end

  def schema_symbol(symbol)
    symbol == 'Rs' ?  'INR' : symbol
  end

  # Determines whether current page is from the checkout flow
  #
  def checkout_flow_page?
    (controller_name == "orders" && action_name != "index") || (controller_name == 'addresses')
  end

  def checkout_cart_login?
    (controller_name == "sessions" && action_name == "guest_login") || (controller_name == "carts")
  end

  def login_page?
    (controller_name == "sessions" && action_name == "new")
  end

  # This method check specific browser
  # Need to change method name, This is for just testing
  def opera_mini_browser?
    browser.opera_mini?
  end

  # This method check for native android lower version browser
  def native_android_browser?
    browser.platform.android? && browser.chrome? && (browser.version.to_i <= 30)
  end

  # This method check if browser is uc browser
  def uc_browser?
    user_agent =  request.env['HTTP_USER_AGENT'].try :downcase
    (user_agent || '').index('ucbrowser') && (user_agent || '').index('midp-2.0')
  end

  def uc_browser_main?
    user_agent =  request.env['HTTP_USER_AGENT'].try :downcase
    (user_agent || '').index('ucbrowser')
  end

  # This method check if browser is safari and version is 7
  def safari_seven_browser?
    browser.safari? && (browser.version.to_i == 7)
  end

  #This method check for lower vesion on iPhone 4
  def safari_four_lower?
    user_agent =  request.env['HTTP_USER_AGENT'].try :downcase
    is_iphone = (user_agent || '').index('iphone') ? true :false
    is_expected_os = (user_agent || '').index('7_1_2') ? true :false
    is_iphone && is_expected_os
  end

  # This version check for safari in app on O.S 8.4
  def safari_in_app
    user_agent =  request.env['HTTP_USER_AGENT'].try :downcase
    is_iphone = (user_agent || '').index('iphone') ? true :false
    is_expected_os = (user_agent || '').index('8_4') ? true :false
    is_facebook_browser = (user_agent || '').index('fbbv') ? true :false
    is_iphone && is_expected_os && is_facebook_browser
  end

  # This method check if browser is firefox browser
  def firefox_browser?
    browser.firefox?
  end

  # This method checks if user is on design details page
  def design_details_page?
    controller_name == "designs" && action_name == "show"
  end

  def return_details_page?
    (['users','orders'].include? params[:controller]) && (['return_orders','index','return_items'].include? params[:action])
  end

  def stitching_information_page?
    params[:controller] == "pages" && params[:action] == "stitching_information"
  end  
  # This method checks if alternate tab is needed in place of mobile menu
  def check_for_alternate_tab?
    checkout_flow_page? || uc_browser? || safari_four_lower? || opera_mini_browser? || safari_in_app
  end

  # This method check for old browser except opera mini
  def old_browser_except_opera_mini
    checkout_flow_page? || uc_browser? || safari_four_lower? || safari_in_app && !opera_mini_browser?
  end

  # This method check for android four browsers
  def android_four_browser?
    browser.platform.android?  && (browser.name.downcase.include? "android") && (browser.version.to_i == 4)
  end
  # This method check for android
  def is_android?
    user_agent = request.env['HTTP_USER_AGENT'].try :downcase
    (uc_browser? || (user_agent || '').index('android')) ? true :false
  end

  def is_mobile_view?
    browser.device.mobile? || browser.device.iphone? || browser.device.tablet?
  end

  # This method checks if user is on design details page
  def design_details_page?
    controller_name == "designs" && action_name == "show"
  end

  def calculate_shipping_approx(design, country)
    weight = design['categories'].max_by{|d| d['weight']}.try(:[],'weight').to_i
    shipping_cost = Order.shipping_cost_for(weight, country)
    additional_cost = Order.shipping_cost_for(weight*2, country)
    single_item_value = get_price_with_symbol_currency(shipping_cost, @hex_symbol, true)
    additional_value = get_price_with_symbol_currency((additional_cost - shipping_cost),@hex_symbol, true)
    return [single_item_value, additional_value]
  end

  def coupon_text(coupon, hex_symbol=nil, exclude_min_amount=false)
    if !exclude_min_amount && (coupon.min_amount.present? && coupon.min_amount > 10)
      if coupon.coupon_type == "POFF"
        coupon_text = coupon.percent_off.to_s + '% off on ' + get_price_with_symbol_currency(coupon.min_amount, @hex_symbol).to_s
      else
        coupon_text = get_price_with_symbol_currency(coupon.flat_off, @hex_symbol) + ' off on ' + get_price_with_symbol_currency(coupon.min_amount, @hex_symbol).to_s
      end
    else
      if coupon.coupon_type == "POFF"
        coupon_text =  exclude_min_amount ? "#{coupon.percent_off.to_s}% OFF" : coupon.percent_off.to_s + '% off'
      elsif coupon.coupon_type == 'STITOFF'
        coupon_text = 'Stitching FREE'
      else
        coupon_text = exclude_min_amount ? "#{get_price_with_symbol_currency(coupon.flat_off, hex_symbol)} OFF" : get_price_with_symbol_currency(coupon.min_amount, @hex_symbol).to_s + ' flat off '
      end
    end
  end

  def get_addons(line_item_id)
    addons = LineItemAddon.where(:line_item_id => line_item_id)
    items = Array.new
    addons.each do |addon|
      items << {:name => addon.addon_type_value.name, :price => addon.snapshot_price, :prod_time => addon.addon_type_value.prod_time, :notes => addon.notes, :id => addon.id}
    end
    if items.count > 0
      return items
    else
      return false
    end
  end
  
  def get_sub_total_in_currency(price,quantity)
    value = get_price_in_currency(price) * quantity
  end

  def offer_message_pages?
    (params[:controller]  == 'pages' && params[:action] == 'home') ||
    (params[:controller]  == 'store' && params[:action] == 'catalog_page') ||
    (params[:controller]  == 'wishlists' && params[:action] == 'index')
  end

  def landing_pages?
    ['store','pages'].include?(controller_name) && ['dynamic_landing_page','landing','eid','landing_page'].include?(action_name)
  end

  def coupon_offer_page?
    controller_name == "pages" && ['coupons','offers'].include?(action_name)
  end

  def turbolinks_active_page?
    offer_message_pages? || design_details_page? || landing_pages? || stitching_information_page? || coupon_offer_page?
  end

  def has_search_bar?
    offer_message_pages? || (controller_name == "registrations" && action_name == "new") || (controller_name == "pages" && ['coupons','offers','eid'].include?(action_name)) || (params[:controller]  == 'store' && params[:action] == 'dynamic_landing_page')
  end
  
  def storefront_page?
    ['pages', 'store'].include?(controller_name) && ['home', 'catalog_page'].include?(action_name)
  end  

  def webp_picture_tag(pclip_object, options={})
    style_name = options.delete(:p_style)
    style_type = pclip_object.content_type || 'image/jpeg'
    if WEBP_CONFIGURATION['switch'] && style_name.present?
      sup_time = WEBP_CONFIGURATION[pclip_object.instance.class.to_s]
      if sup_time.nil? || (sup_time.is_a?(Time) && pclip_object.instance.created_at > sup_time)
        webp_style_name = pclip_object.styles.keys.find{|style| style == "#{style_name}_webp".to_sym}
      end
    end
    if options[:lazy].is_a?(Hash)
      lazy = options.delete(:lazy)
      lazy = {p_holder: asset_path('data:image/gif;base64,R0lGODlhAQABAIAAAAUEBAAAACwAAAAAAQABAAACAkQBADs='), class: 'lazy', source_attr: {src: 'data-src', srcset: 'data-srcset'}}.deep_merge!(lazy.to_h)
      options[lazy[:source_attr][:src]] = pclip_object.url(style_name)
      if webp_style_name.present?
        content_tag :picture, class: lazy[:class] do
          content_tag(:source, '', srcset: lazy[:p_holder], type: 'image/webp', lazy[:source_attr][:srcset] => pclip_object.url(webp_style_name)) +
          content_tag(:source, '', srcset: lazy[:p_holder], type: style_type, lazy[:source_attr][:srcset] => pclip_object.url(style_name)) +
          image_tag(lazy[:p_holder], options)
        end
      else
        options[:class] = [options[:class], lazy[:class]].compact.join(' ')
        image_tag(lazy[:p_holder],  options)
      end
    else
      if webp_style_name.present?
        content_tag :picture do
          content_tag(:source, '', srcset: pclip_object.url(webp_style_name), type: 'image/webp') +
          content_tag(:source, '', srcset: pclip_object.url(style_name), type: style_type) +
          image_tag(pclip_object.url(style_name), options)
        end
      else
        image_tag(pclip_object.url(style_name), options)
      end
    end
  end

  def picture_tag(url, options={})
    style_name = options.delete(:p_style)
    style_type = options[:content_type] || 'image/jpeg'
    if WEBP_CONFIGURATION['switch'] && style_name.present?
      webp_style_name = url.keys.find{|style| style == "#{style_name}_webp"}
    end
    if options[:lazy].is_a?(Hash)
      lazy = options.delete(:lazy)
      lazy = {p_holder: BASE_64_PLACHOLDER_IMAGE, class: 'js-lazy', source_attr: {src: 'data-original', srcset: 'data-original'}}.deep_merge!(lazy.to_h)
      options[lazy[:source_attr][:src]] = url["#{style_name}"]
      options[:class] = [options[:class], lazy[:class]].compact.join(' ')
      if webp_style_name.present?
        content_tag :picture do
          content_tag(:source, '', srcset: lazy[:p_holder], type: 'image/webp', lazy[:source_attr][:srcset] => url[webp_style_name], class: lazy[:class]) +
          content_tag(:source, '', srcset: lazy[:p_holder], type: style_type, lazy[:source_attr][:srcset] => url["#{style_name}"], class: lazy[:class]) +
          image_tag(lazy[:p_holder], options)
        end
      else
        image_tag(lazy[:p_holder],  options)
      end
    else
      if webp_style_name.present?
        content_tag :picture do
          content_tag(:source, '', srcset: url[webp_style_name], type: 'image/webp') +
          content_tag(:source, '', srcset: url["#{style_name}"], type: style_type) +
          image_tag(url["#{style_name}"], options)
        end
      else
        image_tag(url["#{style_name}"], options)
      end
    end
  end

  def async_stylesheet_tag(source, attributes={})
    file_name, extension = source.split('.')
    if THEME_CSS_FILE_MAPPING.has_key?(file_name) && AbTesting.current_theme.present?
      file_name = THEME_CSS_FILE_MAPPING[file_name][AbTesting.current_theme] || file_name
    end
    source = extension.present? ? "#{file_name}.#{extension}" : "#{file_name}.css"
    url = asset_url(source)
    javascript_tag do
     "loadStyle('#{url}', #{attributes.to_json.html_safe})".html_safe
    end
  end

  def amp_url_for(link)
    if link == "/"
      root_amp_url
    else
      url, query_string =link.split('?')
      query =  Rack::Utils.parse_nested_query(query_string)
      query.present? ? "#{url}/amp?#{query.to_param}" : "#{url}/amp"
    end
  end

  def mirraw_contact_number(current_currency=nil, current_country=nil)
    symbol = current_currency.present? ? current_currency : @symbol
    country_code = current_country.present? ? current_country : @country_code
    if ['inr', 'rs'].include?(symbol.try(:downcase))
      return MIRRAW_CONTACT_INFO
    elsif symbol.try(:downcase) == 'cad'
      return '+15-817-054-535'
    elsif country_code.to_s.downcase == 'gb'
      return '+441-214-614-192'
    else
      return '+1-949-464-5941'
    end
  end

  def catalog_hit?
    controller_name == 'store' and ['catalog_page', 'catalog_page_amp'].include?(action_name)
  end

  def category_page?
    catalog_hit? and params[:kind].present?
  end
  
  def search_query?
    catalog_hit? and params[:q].present?
  end

  def webp_format_image(image_link)
    image_link.to_s.gsub(/\.(jpg|jpeg|JPG|JPEG|png|PNG)/, '.webp')
  end

  def ga_event_script_hash(events)
    Array.wrap(events).map do |event|
      ga_event = { user_id: event['user_id'] }
      ga_event[:event] = event['action'] if event['action'].present?
      ga_event[:method] = event['method'] if event['method'].present?
      ga_event[:ecommerce] = nil if event['action'].present?
      "dataLayer.push(#{ga_event.to_json});"
    end.join("\n")
  end

end
