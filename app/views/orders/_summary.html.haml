- if (@order.cod? || @order.cbd?) && (COD_NOTE_MESSAGE != 'false')
  .row
    .columns
      .alert-box.radius.info
        = "#{COD_NOTE_MESSAGE}"
.text-center Order Summary :
.row
  .columns.bordered_block
    .row
      .columns
        %table
          %tbody
            %tr
              %td State :
              %td= @order.customer_state
            %tr
              %td Item Total :
              %td= get_price_with_symbol(@order.item_total_without_addons(currency_details[:rate]), currency_details[:hex_symbol])
            %tr
              %td Discounts :
              %td= get_price_with_symbol(@order.total_discount_currency(currency_details[:rate]), currency_details[:hex_symbol])
            - addons = @order.addons_total(currency_details[:rate])
            - if addons > 0
              %tr
                %td Customisation :
                %td= get_price_with_symbol(addons, currency_details[:hex_symbol])
            %tr
              - shipping_cost = @order.shipping_currency(currency_details[:rate])
              - shipping_cost += @order.express_delivery_currency(currency_details[:rate]) if @order.express_delivery?
              %td Shipping Charges :
              %td= get_price_with_symbol(shipping_cost, currency_details[:hex_symbol])
            %tr
              %td COD Charges :
              %td= get_price_with_symbol(@order.cod_charge_currency(currency_details[:rate]), currency_details[:hex_symbol])
            - if (order_addon = @order.order_addon).present?
              %tr
                %td Gift Wrap Charges :
                %td= get_price_with_symbol(@order.order_addon.gift_wrap_price_currency(currency_details[:rate]), currency_details[:hex_symbol])
            - if @order.total_tax > 0
              %tr
                %td Total Tax :
                %td= get_price_with_symbol(@order.total_tax_currency(currency_details[:rate]), currency_details[:hex_symbol])
            %tr
              %td Total :
              %td= get_price_with_symbol(@order.total_currency(currency_details[:rate]), currency_details[:hex_symbol])
            -if @order.state == 'dispatched'
              %tr
                %td Courier Company :
                %td= @order.courier_company
              %tr
                %td Tracking Number :
                %td= @order.tracking_number

      - if RAKHI_PRE_ORDER[0] == 'true'
        .columns
          - rakhi_pre = @order.rakhi_pre_order
          - if @order.country == 'India' && rakhi_pre['rakhi_with_other_designs'].present?
            .panel.callout.radius= "Your Order has multiple items, only Rakhi will be delivered between #{RAKHI_PRE_ORDER[1]}."
          - elsif rakhi_pre['rakhi_all_schedule'].present? || rakhi_pre['rakhi_with_other_designs'].present?
            .panel.callout.radius= "Your order will be delivered between #{RAKHI_PRE_ORDER[1]}."