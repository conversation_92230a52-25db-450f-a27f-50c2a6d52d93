class CartsController < ApplicationController
  include CurrencyHelper
  
  ASSOCIATIONS = [line_items: [design: [:designer, :master_image, :categories], variant: [
  option_type_values: :option_type], line_item_addons: [addon_type_value: :addon_option_types]]]

  before_action :featured_products, only: [:show, :assign_coupon]
  before_action :current_web_cart, only:[:generate_otp, :verify_otp]

  # Provides User Cart
  #
  # == Returns:
  # ActiveRecord Object
  #
  def show
    session[:cart_id] = Cart.where(hash1: params[:id]).first.try(:id) if params[:id].present?
    @cart = current_web_cart(ASSOCIATIONS, session[:cart_id])
    @offers = @cart.conditional_offers(@rate, @actual_country)
    @free_stitching_text = @cart.free_stitching_text(@country_code, @rate, @symbol)
    @cart_minimum_value =
    RequestStore.cache_fetch("minimum_cart_value_#{@country_code}", expires_in: 24.hours) do
      CurrencyConvert.to_currency(@country_code, INTERNATIONAL_MIN_CART_VAL.to_i).round(2)
    end
    gtm_data_layer.push({pageType: 'cart'}.merge!(@cart.details_for_gtm))
    @cart.update_column(:coupon_id, nil) if @cart.coupon.present? && @cart.line_items.blank?
    @current_user = current_user
    @cart.remove_existing_referral(current_user)
    @cart.remove_existing_return
    cart_ga_hash_v2
  end

  def generate_otp
    otp_sent = false
    address_id = params[:address_id].to_i
    if address_id > 0 && address = Address.find_by_id(address_id)
      phone = params[:phone].presence || address.phone
      if phone.present? && (phone == address.phone || address.update_attributes(phone: phone))
        resend = params[:resend]
        otp_sent = @cart.try{|cart| cart.generate_otp(phone, resend)}
      end
    end
    render json: {otp_sent: otp_sent.present?}
  end

  def verify_otp
    verified = @cart.try(:otp) == params['cod-otp'].to_s
    @cart.update_attribute(:otp, 'verified') if verified
    render json: {verified: verified}
  end

  def select_coupon_code
    coupon_type = @country_code.present? && @country_code.downcase.eql?("in") ? "Domestic" : "International"  
    @available_coupons = Coupon.where(geo: [coupon_type,"All"]).live.mirraw_coupons.by_source('website').advertise
    @available_coupons = @available_coupons.select { |coupon| coupon.applicable_for_country?(@country_code) }
    cart = current_web_cart(ASSOCIATIONS, session[:cart_id])
  end

  def apply_coupon
    cart = current_web_cart(ASSOCIATIONS, session[:cart_id])
    coupon_type = @country_code.present? && @country_code.downcase.eql?("in") ? "Domestic" : "International"  
    applied_coupon_code = if params[:applied_coupon].present?
                            params[:applied_coupon]
                          elsif params[:hidden_coupon].present?
                            params[:hidden_coupon]
                          end
    coupon = Coupon.where("LOWER(code) = ?", applied_coupon_code.downcase).where("geo = ? OR geo = ?", coupon_type , "All").mirraw_coupons.by_source('website').first if (applied_coupon_code.present? && coupon_type.present?)
    if coupon.blank? 
      cart.errors.add(:coupon, 'not found')
    elsif !coupon.applicable_for_country?(@country_code)
      cart.errors.add(:coupon, 'not valid for your country')
    elsif cart.assign_check_coupon?(coupon)
      cart.update(wallet_id: nil)
    end
    cart_errors = amend_coupon_errors(cart, coupon)
    if cart_errors.any?
      flash[:error] = cart_errors.join(', ')
      redirection_path = select_coupon_code_path
    else
      flash[:notice] = t('coupon.success')
      redirection_path = cart_path
    end
    redirect_to redirection_path
  end

  def remove_coupon
    cart = current_web_cart(ASSOCIATIONS, session[:cart_id])
    cart.update(coupon_id: nil)
    redirect_to cart_path
  end

  def cart_ga_hash_v2
    @ga_hash_new = {}
    design_prices = []
    market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
    b1g1_offers = ApplicationController.helpers.get_price_in_currency(@cart.bmgnx_discounts(bmgnx_hash = PromotionPipeLine.bmgnx_hash), @rate)
    b1g1_offers = (b1g1_offers *= market_rate).round(CurrencyConvert.round_to)
    @ga_hash_new['value'] = @cart.total_currency(1)
    @ga_hash_new['tax'] = 0 
    @ga_hash_new['offers_discount'] = b1g1_offers
    @ga_hash_new['shipping'] = @cart.shipping_cost_currency(nil,1)
    @ga_hash_new['currency'] = 'INR'
    @ga_hash_new['coupon'] = @cart.coupon.try(:code) || ""
    @ga_hash_new['coupon_discount'] = 0
    @ga_hash_new['country_code'] = @country_code
  
    @ga_hash_new['items'] =  @cart.line_items.map do |line_item|
        design = Design.where(id: line_item.design_id).first
        category = design.categories.first.breadcrumb_path.keys
        category_title = design.categories.first.title
        category_id = design.categories.first.id
        line_item_data = line_item.ga_data
        line_item_data['item_category'] = category[1] || ""
        line_item_data['item_category2'] = category[2] || ""
        line_item_data['item_category3'] = category[3] || ""
        line_item_data['item_category4'] = category[4] || ""
        line_item_data['item_category5'] = category[5] || ""
        line_item_data["content_category"] = "#{line_item.design.categories.first.name}".titleize
        design_prices << (line_item_data['price'] * line_item.quantity)
        line_item_data
    end
    @totalvalue = design_prices.count() > 1 ? design_prices.sum : design_prices[0] || 0
    @totalvalue = (@totalvalue - b1g1_offers).round(CurrencyConvert.round_to)
    @pagetype = "add_payment_info"
  end

  def add_gift_wrap_price
    session[:gift_wrap] = session[:gift_wrap].nil? ? true : !session[:gift_wrap]
    respond_to do |format|
      format.json {
        render json: {redirect_url: cart_url}
      }
    end
  end

  def save_email
    begin
      @cart = Cart.find_by_id params[:cart_id]
      if @cart and @cart.id < 0
        session[:cart_email] = params[:email]
      else
        @cart.email = params[:email]
        unless @cart.save
          render :js => {:errors => @cart.errors}
          return
        end
      end
      render json: {success: '1', status: '200'}
    rescue Exception => e
      render json: {success: '0', notice: 'Oops! Something went wrong.'}
    end
    return
  end

  private

  def amend_coupon_errors(cart, coupon)
    cart.errors.full_messages.map do |error_message|
      if error_message['minimum']
        "#{error_message} Make sure you have items worth more than #{get_symbol_from(@hex_symbol)}#{get_price_in_currency(coupon.min_amount, @rate)}."
      else
        error_message
      end
    end
  end
end
