module Payment
  module Paypal
    class PaypalApiData

      def initialize(order, country_code, currency_symbol)
        @order = order
        country_code, @country_symbol = (Order::PAYPAL_ALLOWED_CURRENCIES).include?(currency_symbol) ? [country_code, currency_symbol] : ['US', 'USD']
        @payment_details = Payment::PaymentDetails.new(order, country_code, @country_symbol)
      end
      
      def get_checkout_v2_request_payload
        return_url = "/orders/#{@payment_details.order_reference}"
        item_details, amount_hash = get_v2_item_details

        { 
          "intent": "CAPTURE",
          "payer": {
              "name": {
                  "given_name": @payment_details.get_first_name,
                  "surname": @payment_details.get_last_name
              },
              "email_address": @payment_details.get_email,
              "address": {
                  "address_line_1": @payment_details.get_address1,
                  "address_line_2": @payment_details.get_address2,
                  "admin_area_1": @payment_details.get_state_code,
                  "admin_area_2": @payment_details.get_city,
                  "postal_code": @payment_details.get_zip,
                  "country_code": (country = Country.find_by_namei(@payment_details.billing_country)).try(:[], :iso3166_alpha2)
              }
          },
          "purchase_units": [
              {
                  "reference_id": "default",
                  "description": "",
                  "custom_id": "#{@payment_details.get_first_name}-#{@payment_details.order_reference}",
                  "amount": amount_hash,
                  "items": item_details,
                  "shipping": {
                      "address": get_checkout_v2_shipping_address
                  },
                  "invoice_id": "#{@payment_details.order_reference}"
              }
          ],
          "application_context": get_application_contex
      }
      end
    
      def get_application_contex
        base_url = @order.app_source.downcase.include?('ios') ? ENV["PAYPAL_IOS_RETURN_URL"] : ENV["PAYPAL_ANDROID_RETURN_URL"]

        return_url = base_url + '/orders/' + @payment_details.order_reference
        cancel_url = base_url + '/order/' +"#{@payment_details.reference_id}"
        {
          "brand_name": "Mirraw",
          "shipping_preference": "SET_PROVIDED_ADDRESS",
          "user_action": "PAY_NOW",
          "payment_method": {
              "payer_selected": "PAYPAL",
              "payee_preferred": "IMMEDIATE_PAYMENT_REQUIRED"
          },
    
          "return_url": return_url,
          "cancel_url": cancel_url
      }
      end
    
      def get_checkout_v2_shipping_address
        {
          "address_line_1": @payment_details.get_shipping_line1,
          "address_line_2": @payment_details.get_shipping_line2,
          "postal_code": @payment_details.get_shipping_postal_code,
          "country_code": (country = Country.find_by_namei(@payment_details.order_country)).try(:[], :iso3166_alpha2),
          "admin_area_2": @payment_details.get_shipping_city
      }
      end
    
      def get_v2_item_details()
        item_total, item_details = @payment_details.get_order_item_details
        amount_hash = {}
        amount_hash["amount"] = {}
        amount_hash["amount"]['breakdown'] = {}
        discount_price = @payment_details.get_discount_price
        tax_amount = @payment_details.get_tax_amount
        shipping_discount = discount_price if discount_price
        shipping = @payment_details.get_shipping_cost
        grand_total = (item_total + shipping - shipping_discount.to_f).round(2)
        # total_with_taxes  =   (grand_total + tax_amount).round(2)
        grand_total = (grand_total + tax_amount).round(2) 
        amount_hash["amount"]["value"] = "#{grand_total}"
        amount_hash["amount"]["currency_code"] = @country_symbol
        amount_hash["amount"]['breakdown']['item_total'] = {
          "value" => "#{item_total}",
          "currency_code": @country_symbol
        }
        amount_hash["amount"]['breakdown']['shipping'] = {
          "value" => "#{shipping}",
          "currency_code": @country_symbol
        }
        amount_hash["amount"]['breakdown']['shipping_discount'] = {
          "value" => "#{shipping_discount}",
          "currency_code": @country_symbol
        }
        amount_hash["amount"]['breakdown']['tax_total'] = {
          "value" => "#{tax_amount}",
          "currency_code": @country_symbol
        }
        return [item_details, amount_hash["amount"]]
      end
    end
  end
end
